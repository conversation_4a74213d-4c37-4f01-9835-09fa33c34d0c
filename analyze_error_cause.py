"""
分析 "Set changed size during iteration" 错误的具体原因
"""
import openpyxl
from openpyxl import Workbook

def demonstrate_error():
    """演示错误发生的具体原因"""
    print("=== 分析 'Set changed size during iteration' 错误原因 ===\n")
    
    # 创建一个测试工作簿
    wb = Workbook()
    ws = wb.active
    
    # 创建一些合并单元格
    ws.merge_cells('A1:B1')
    ws.merge_cells('C1:D1')
    ws.merge_cells('A2:B2')
    
    print(f"初始合并单元格数量: {len(ws.merged_cells.ranges)}")
    print("合并单元格范围:", [str(r) for r in ws.merged_cells.ranges])
    
    print("\n=== 演示错误的代码（会出错）===")
    try:
        # 这是会出错的代码
        for merged_range in ws.merged_cells.ranges:
            print(f"正在处理: {merged_range}")
            print(f"当前ranges大小: {len(ws.merged_cells.ranges)}")
            ws.unmerge_cells(str(merged_range))
            print(f"unmerge后ranges大小: {len(ws.merged_cells.ranges)}")
    except RuntimeError as e:
        print(f"❌ 错误发生: {e}")
    
    print("\n=== 正确的代码（不会出错）===")
    # 重新创建合并单元格用于演示正确方法
    ws.merge_cells('A3:B3')
    ws.merge_cells('C3:D3')
    ws.merge_cells('A4:B4')
    
    print(f"重新创建后合并单元格数量: {len(ws.merged_cells.ranges)}")
    
    # 正确的方法：创建副本
    merged_ranges_copy = list(ws.merged_cells.ranges)
    print(f"副本大小: {len(merged_ranges_copy)}")
    
    for merged_range in merged_ranges_copy:
        print(f"正在处理: {merged_range}")
        print(f"原始ranges大小: {len(ws.merged_cells.ranges)}")
        ws.unmerge_cells(str(merged_range))
        print(f"unmerge后原始ranges大小: {len(ws.merged_cells.ranges)}")
        print(f"副本大小保持不变: {len(merged_ranges_copy)}")
    
    print("✅ 正确方法执行成功!")

def analyze_openpyxl_behavior():
    """分析openpyxl的具体行为"""
    print("\n=== 分析openpyxl merged_cells.ranges的行为 ===")
    
    wb = Workbook()
    ws = wb.active
    ws.merge_cells('A1:B1')
    ws.merge_cells('C1:D1')
    
    print(f"merged_cells.ranges的类型: {type(ws.merged_cells.ranges)}")
    print(f"是否为动态集合: {hasattr(ws.merged_cells.ranges, '__iter__')}")
    
    # 检查ranges是否在unmerge时立即更新
    original_ranges = ws.merged_cells.ranges
    print(f"unmerge前的id: {id(original_ranges)}")
    
    ranges_list = list(ws.merged_cells.ranges)
    first_range = ranges_list[0]
    
    print(f"准备unmerge: {first_range}")
    ws.unmerge_cells(str(first_range))
    
    print(f"unmerge后的id: {id(ws.merged_cells.ranges)}")
    print(f"ranges对象是否相同: {original_ranges is ws.merged_cells.ranges}")
    
def explain_why_now():
    """解释为什么现在才出现这个问题"""
    print("\n=== 为什么现在才出现这个问题？ ===")
    
    print("可能的原因：")
    print("1. **openpyxl版本更新**:")
    print(f"   - 当前版本: {openpyxl.__version__}")
    print("   - 新版本中merged_cells.ranges可能改为动态更新")
    print("   - 旧版本可能不会在unmerge时立即更新ranges")
    
    print("\n2. **数据结构变化**:")
    print("   - 模板文件可能包含了更多合并单元格")
    print("   - 以前目标工作表可能没有合并单元格")
    
    print("\n3. **执行路径变化**:")
    print("   - 这个函数以前可能没有被调用")
    print("   - 或者调用时的条件不同")
    
    print("\n4. **Python版本影响**:")
    print("   - Python 3.11可能对集合修改检测更严格")
    print("   - 旧版本Python可能不会抛出这个错误")

if __name__ == "__main__":
    demonstrate_error()
    analyze_openpyxl_behavior()
    explain_why_now()
