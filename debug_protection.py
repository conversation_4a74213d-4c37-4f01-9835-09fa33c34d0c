"""
调试绩效工资表保护功能
"""
import openpyxl

def debug_protection_logic():
    """调试保护逻辑"""
    print("=== 调试绩效工资表保护逻辑 ===\n")
    
    # 加载工作簿
    wb = openpyxl.load_workbook('聘用人员工资表.xlsx')
    target_sheet_name = '绩效工资'
    
    # 检查绩效工资工作表是否存在
    if target_sheet_name not in wb.sheetnames:
        print(f"❌ {target_sheet_name}工作表不存在")
        return
    
    print(f"✅ {target_sheet_name}工作表存在")
    
    # 检查工作表是否为空
    target_sheet = wb[target_sheet_name]
    
    # 找到表头行
    def find_name_row(ws):
        """找到包含'姓名'的行作为首行"""
        for row_idx, row in enumerate(ws.iter_rows(values_only=True), 1):
            for cell in row:
                if cell == '姓名':
                    return row_idx
        return None
    
    header_row = find_name_row(target_sheet)
    print(f"表头行位置: {header_row}")
    
    if not header_row:
        print("❌ 无表头，应该重新生成")
        return
    
    print(f"✅ 找到表头行在第{header_row}行")
    
    # 检查是否有数据行（表头行之后是否有非空数据）
    has_data = False
    data_rows = []
    
    for row_idx, row in enumerate(target_sheet.iter_rows(min_row=header_row + 1), header_row + 1):
        row_has_data = False
        row_data = []
        for cell in row:
            if cell.value is not None and str(cell.value).strip():
                row_has_data = True
                has_data = True
            row_data.append(cell.value)
        
        if row_has_data:
            data_rows.append((row_idx, row_data[:5]))  # 只保存前5列数据
        
        if len(data_rows) >= 10:  # 限制输出
            break
    
    print(f"找到 {len(data_rows)} 行数据:")
    for row_idx, row_data in data_rows[:5]:  # 只显示前5行
        print(f"  第{row_idx}行: {row_data}")
    
    if len(data_rows) > 5:
        print(f"  ... 还有 {len(data_rows) - 5} 行数据")
    
    if has_data:
        print("✅ 工作表包含数据，应该跳过重新生成以保护手动调整的数据")
        return False  # 不需要重新生成
    else:
        print("❌ 工作表无数据，应该重新生成")
        return True  # 需要重新生成

if __name__ == "__main__":
    debug_protection_logic()
