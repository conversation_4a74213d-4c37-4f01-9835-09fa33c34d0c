import openpyxl

print("=== 最终结果测试 ===")

try:
    wb = openpyxl.load_workbook('聘用人员工资表_新.xlsx')
    print('✅ 文件加载成功!')
    print(f'工作表数量: {len(wb.sheetnames)}')
    
    if '工资计税导入表' in wb.sheetnames:
        ws = wb['工资计税导入表']
        print(f'工资计税导入表行数: {ws.max_row}')
        cell = ws.cell(1, 2)
        print(f'居中对齐: 水平={cell.alignment.horizontal}, 垂直={cell.alignment.vertical}')
        print(f'字体大小: {cell.font.size}')
    
    wb.close()
    print('🎉 文件完全正常，没有损坏!')
    
except Exception as e:
    print(f'❌ 文件损坏: {e}')
    import traceback
    traceback.print_exc()
