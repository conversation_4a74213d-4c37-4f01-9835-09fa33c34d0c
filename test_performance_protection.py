"""
测试绩效工资表保护功能的完整测试脚本
"""
import openpyxl
import os
import shutil

def test_performance_protection():
    """测试绩效工资表保护功能"""
    print("=== 绩效工资表保护功能测试 ===\n")
    
    # 测试场景1：绩效工资表不存在时应该创建
    print("📋 测试场景1：绩效工资表不存在")
    
    # 备份原始模板
    if os.path.exists('聘用人员工资表.xlsx'):
        shutil.copy('聘用人员工资表.xlsx', '聘用人员工资表_backup.xlsx')
    
    # 从模板复制一个新的文件
    shutil.copy('模版/聘用人员工资表 - 模版.xlsx', '聘用人员工资表.xlsx')
    
    # 确认绩效工资表不存在
    wb = openpyxl.load_workbook('聘用人员工资表.xlsx')
    if '绩效工资' in wb.sheetnames:
        wb.remove(wb['绩效工资'])
        wb.save('聘用人员工资表.xlsx')
    
    print("  ✅ 已删除绩效工资表，准备测试创建功能")
    
    # 运行程序
    print("  🔄 运行程序...")
    os.system('python main.py > test_output1.txt 2>&1')
    
    # 检查结果
    wb_result = openpyxl.load_workbook('聘用人员工资表_新.xlsx')
    if '绩效工资' in wb_result.sheetnames:
        ws = wb_result['绩效工资']
        # 检查是否有数据
        has_data = False
        for row in ws.iter_rows(min_row=2):
            for cell in row:
                if cell.value is not None and str(cell.value).strip():
                    has_data = True
                    break
            if has_data:
                break
        
        if has_data:
            print("  ✅ 测试通过：绩效工资表被成功创建并包含数据")
        else:
            print("  ❌ 测试失败：绩效工资表被创建但没有数据")
    else:
        print("  ❌ 测试失败：绩效工资表未被创建")
    
    print()
    
    # 测试场景2：绩效工资表存在且有数据时应该保护
    print("📋 测试场景2：绩效工资表存在且有手动调整数据")
    
    # 在绩效工资表中添加手动调整的数据
    wb = openpyxl.load_workbook('聘用人员工资表_新.xlsx')
    if '绩效工资' in wb.sheetnames:
        ws = wb['绩效工资']
        
        # 添加测试数据
        test_row = 15
        ws.cell(row=test_row, column=1, value=888)
        ws.cell(row=test_row, column=2, value='保护测试科室')
        ws.cell(row=test_row, column=3, value='保护测试员工')
        ws.cell(row=test_row, column=4, value=7777.77)
        
        # 保存到输入文件
        wb.save('聘用人员工资表.xlsx')
        print("  ✅ 已添加手动调整数据")
    
    # 再次运行程序
    print("  🔄 再次运行程序...")
    os.system('python main.py > test_output2.txt 2>&1')
    
    # 检查手动调整的数据是否被保护
    wb_result = openpyxl.load_workbook('聘用人员工资表_新.xlsx')
    if '绩效工资' in wb_result.sheetnames:
        ws = wb_result['绩效工资']
        test_row = 15
        
        col2_value = ws.cell(row=test_row, column=2).value
        col3_value = ws.cell(row=test_row, column=3).value
        col4_value = ws.cell(row=test_row, column=4).value
        
        if (col2_value == '保护测试科室' and 
            col3_value == '保护测试员工' and 
            col4_value == 7777.77):
            print("  ✅ 测试通过：手动调整的数据被成功保护")
        else:
            print("  ❌ 测试失败：手动调整的数据丢失")
            print(f"    期望: 保护测试科室, 保护测试员工, 7777.77")
            print(f"    实际: {col2_value}, {col3_value}, {col4_value}")
    else:
        print("  ❌ 测试失败：绩效工资表不存在")
    
    print()
    
    # 检查程序输出
    print("📋 检查程序输出信息")
    
    # 检查第一次运行的输出
    if os.path.exists('test_output1.txt'):
        with open('test_output1.txt', 'r', encoding='utf-8') as f:
            content1 = f.read()
            if '绩效工资工作表不存在，正在创建并复制数据' in content1:
                print("  ✅ 第一次运行：正确识别工作表不存在并创建")
            else:
                print("  ❌ 第一次运行：未正确识别工作表状态")
    
    # 检查第二次运行的输出
    if os.path.exists('test_output2.txt'):
        with open('test_output2.txt', 'r', encoding='utf-8') as f:
            content2 = f.read()
            if '绩效工资工作表已存在且包含数据，跳过重新生成以保护手动调整的数据' in content2:
                print("  ✅ 第二次运行：正确识别工作表存在数据并保护")
            else:
                print("  ❌ 第二次运行：未正确识别工作表状态")
    
    # 清理测试文件
    print("\n🧹 清理测试文件...")
    for file in ['test_output1.txt', 'test_output2.txt']:
        if os.path.exists(file):
            os.remove(file)
    
    # 恢复备份
    if os.path.exists('聘用人员工资表_backup.xlsx'):
        shutil.copy('聘用人员工资表_backup.xlsx', '聘用人员工资表.xlsx')
        os.remove('聘用人员工资表_backup.xlsx')
        print("  ✅ 已恢复原始文件")
    
    print("\n🎯 测试完成！")

if __name__ == "__main__":
    test_performance_protection()
