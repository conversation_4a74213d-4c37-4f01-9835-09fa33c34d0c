"""
解决中文路径问题的脚本
"""
import os
import shutil

def create_english_workspace():
    """创建英文工作目录"""
    # 创建英文目录
    english_path = "D:/salary_reports"
    
    if not os.path.exists(english_path):
        os.makedirs(english_path)
        print(f"✅ 已创建英文工作目录: {english_path}")
    
    # 复制必要文件
    files_to_copy = [
        "main.py",
        "科室绩效汇总.xlsx",
        "模版/聘用人员工资表 - 模版.xlsx"
    ]
    
    for file_path in files_to_copy:
        if os.path.exists(file_path):
            filename = os.path.basename(file_path)
            if filename == "聘用人员工资表 - 模版.xlsx":
                # 重命名为英文
                target_path = os.path.join(english_path, "template.xlsx")
            else:
                target_path = os.path.join(english_path, filename)
            
            shutil.copy2(file_path, target_path)
            print(f"已复制: {file_path} -> {target_path}")
    
    print(f"\n请切换到英文目录运行程序:")
    print(f"cd {english_path}")
    print("python main.py")
    
    return english_path

if __name__ == "__main__":
    create_english_workspace()
