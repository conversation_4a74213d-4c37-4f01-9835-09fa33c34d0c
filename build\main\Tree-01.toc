('D:/Users/<USER>/miniconda3/Library/lib\\tk8.6',
 'tk',
 ['demos', '*.lib', 'tkConfig.sh'],
 'DATA',
 [('tk\\bgerror.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\bgerror.tcl',
   'DATA'),
  ('tk\\button.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\button.tcl',
   'DATA'),
  ('tk\\choosedir.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('tk\\clrpick.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\clrpick.tcl',
   'DATA'),
  ('tk\\comdlg.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\comdlg.tcl',
   'DATA'),
  ('tk\\console.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\console.tcl',
   'DATA'),
  ('tk\\dialog.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\dialog.tcl',
   'DATA'),
  ('tk\\entry.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\entry.tcl',
   'DATA'),
  ('tk\\focus.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\focus.tcl',
   'DATA'),
  ('tk\\fontchooser.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('tk\\iconlist.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\iconlist.tcl',
   'DATA'),
  ('tk\\icons.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\icons.tcl',
   'DATA'),
  ('tk\\license.terms',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\license.terms',
   'DATA'),
  ('tk\\listbox.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\listbox.tcl',
   'DATA'),
  ('tk\\megawidget.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('tk\\menu.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\menu.tcl',
   'DATA'),
  ('tk\\mkpsenc.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\mkpsenc.tcl',
   'DATA'),
  ('tk\\msgbox.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\msgbox.tcl',
   'DATA'),
  ('tk\\obsolete.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\obsolete.tcl',
   'DATA'),
  ('tk\\optMenu.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\optMenu.tcl',
   'DATA'),
  ('tk\\palette.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\palette.tcl',
   'DATA'),
  ('tk\\panedwindow.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('tk\\pkgIndex.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\pkgIndex.tcl',
   'DATA'),
  ('tk\\safetk.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\safetk.tcl',
   'DATA'),
  ('tk\\scale.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\scale.tcl',
   'DATA'),
  ('tk\\scrlbar.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\scrlbar.tcl',
   'DATA'),
  ('tk\\spinbox.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\spinbox.tcl',
   'DATA'),
  ('tk\\tclIndex',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\tclIndex',
   'DATA'),
  ('tk\\tearoff.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\tearoff.tcl',
   'DATA'),
  ('tk\\text.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\text.tcl',
   'DATA'),
  ('tk\\tk.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\tk.tcl',
   'DATA'),
  ('tk\\tkfbox.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\tkfbox.tcl',
   'DATA'),
  ('tk\\unsupported.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('tk\\xmfbox.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\xmfbox.tcl',
   'DATA'),
  ('tk\\ttk\\altTheme.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('tk\\ttk\\aquaTheme.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('tk\\ttk\\button.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('tk\\ttk\\clamTheme.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('tk\\ttk\\classicTheme.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('tk\\ttk\\combobox.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('tk\\ttk\\cursors.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('tk\\ttk\\defaults.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('tk\\ttk\\entry.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('tk\\ttk\\fonts.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('tk\\ttk\\menubutton.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('tk\\ttk\\notebook.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('tk\\ttk\\panedwindow.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('tk\\ttk\\progress.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('tk\\ttk\\scale.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('tk\\ttk\\scrollbar.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('tk\\ttk\\sizegrip.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('tk\\ttk\\spinbox.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('tk\\ttk\\treeview.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('tk\\ttk\\ttk.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\ttk\\ttk.tcl',
   'DATA'),
  ('tk\\ttk\\utils.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('tk\\ttk\\vistaTheme.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('tk\\ttk\\winTheme.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('tk\\ttk\\xpTheme.tcl',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('tk\\msgs\\cs.msg',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\msgs\\cs.msg',
   'DATA'),
  ('tk\\msgs\\da.msg',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\msgs\\da.msg',
   'DATA'),
  ('tk\\msgs\\de.msg',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\msgs\\de.msg',
   'DATA'),
  ('tk\\msgs\\el.msg',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\msgs\\el.msg',
   'DATA'),
  ('tk\\msgs\\en.msg',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\msgs\\en.msg',
   'DATA'),
  ('tk\\msgs\\en_gb.msg',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('tk\\msgs\\eo.msg',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\msgs\\eo.msg',
   'DATA'),
  ('tk\\msgs\\es.msg',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\msgs\\es.msg',
   'DATA'),
  ('tk\\msgs\\fi.msg',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\msgs\\fi.msg',
   'DATA'),
  ('tk\\msgs\\fr.msg',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\msgs\\fr.msg',
   'DATA'),
  ('tk\\msgs\\hu.msg',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\msgs\\hu.msg',
   'DATA'),
  ('tk\\msgs\\it.msg',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\msgs\\it.msg',
   'DATA'),
  ('tk\\msgs\\nl.msg',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\msgs\\nl.msg',
   'DATA'),
  ('tk\\msgs\\pl.msg',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\msgs\\pl.msg',
   'DATA'),
  ('tk\\msgs\\pt.msg',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\msgs\\pt.msg',
   'DATA'),
  ('tk\\msgs\\ru.msg',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\msgs\\ru.msg',
   'DATA'),
  ('tk\\msgs\\sv.msg',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\msgs\\sv.msg',
   'DATA'),
  ('tk\\msgs\\zh_cn.msg',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('tk\\images\\logo.eps',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('tk\\images\\logo100.gif',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('tk\\images\\logo64.gif',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('tk\\images\\logoLarge.gif',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('tk\\images\\logoMed.gif',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('tk\\images\\pwrdLogo.eps',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('tk\\images\\pwrdLogo100.gif',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('tk\\images\\pwrdLogo150.gif',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('tk\\images\\pwrdLogo175.gif',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('tk\\images\\pwrdLogo200.gif',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('tk\\images\\pwrdLogo75.gif',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('tk\\images\\README',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\images\\README',
   'DATA'),
  ('tk\\images\\tai-ku.gif',
   'D:/Users/<USER>/miniconda3/Library/lib\\tk8.6\\images\\tai-ku.gif',
   'DATA')])
